"use client";

import PageContainer from "@/design-system/components/layout/PageContainer";
import { useThemeClassesStatic } from "@/design-system/utils/theme-utils-static";
import useCheckApplication from "@/hooks/candidate/use-check-application";
import useDirectJobApplication from "@/hooks/candidate/use-direct-job-application";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Grid,
  Group,
  List,
  Loader,
  Progress,
  Stack,
  Text,
  ThemeIcon,
  Title,
  Tooltip,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useState } from "react";
import {
  BiBriefcase,
  BiBuildings,
  BiCalendar,
  BiCheckCircle,
  BiDollar,
  BiGlobe,
  BiGroup,
  BiInfoCircle,
  BiMapPin,
  BiSolidGraduation,
  BiTime,
  BiTrophy,
  BiUser,
} from "react-icons/bi";
import { FaCheck, FaRegBookmark, FaRegShareSquare } from "react-icons/fa";
import { useNavigate, useParams } from "react-router";

export default function CandidateJobDetailsPage() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [isButtonLoading, setIsButtonLoading] = useState<{
    save: boolean;
    apply: boolean;
  }>({ save: false, apply: false });
  const { jobDetails, isLoading, error, handleApply, isSubmitting } =
    useDirectJobApplication(id);

  // Check if the candidate has already applied for this job
  const { hasApplied, isChecking } = useCheckApplication(id);

  // Get theme classes function for use in loops/conditions
  const { getClasses } = useThemeClassesStatic();

  // Get company info from job details
  const companyInfo = jobDetails?.companyInfo;

  const handleSaveJob = async () => {
    try {
      setIsButtonLoading((prev) => ({ ...prev, save: true }));
      // In a real implementation, you would call an API to save the job
      // For now, just show a notification
      notifications.show({
        title: "Job Saved",
        message: "This job has been saved to your favorites",
        color: "blue",
      });
    } catch (error: any) {
      notifications.show({
        title: "Error",
        message: error.message || "Failed to save job",
        color: "red",
      });
    } finally {
      setIsButtonLoading((prev) => ({ ...prev, save: false }));
    }
  };

  // Calculate match score based on skills (for demo purposes)
  const matchScore = 92;

  // Show loading state
  if (isLoading) {
    return (
      <PageContainer
        variant="candidate"
        breadcrumbItems={[
          { title: "Home", href: "/" },
          { title: "Jobs", href: "/candidate/jobs" },
          { title: "Job Details" },
        ]}
      >
        <div className="flex flex-col items-center justify-center py-20">
          <Loader size="xl" />
          <Text mt="md" size="lg" fw={500}>
            Loading job details...
          </Text>
        </div>
      </PageContainer>
    );
  }

  // Show error state
  if (error || !jobDetails) {
    return (
      <PageContainer
        variant="candidate"
        breadcrumbItems={[
          { title: "Home", href: "/" },
          { title: "Jobs", href: "/candidate/jobs" },
          { title: "Job Details" },
        ]}
      >
        <Card shadow="sm" radius="md" withBorder className="p-8 text-center">
          <Title order={2} className="mb-4 text-red-500">
            Error Loading Job Details
          </Title>
          <Text size="lg">{error || "Job not found"}</Text>
          <Button
            mt="xl"
            onClick={() => navigate("/candidate/jobs")}
            leftSection={<BiMapPin size={16} />}
          >
            Back to Jobs
          </Button>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      variant="candidate"
      breadcrumbItems={[
        { title: "Home", href: "/" },
        { title: "Jobs", href: "/candidate/jobs" },
        { title: "Job Details" },
      ]}
    >
      {/* Hero Section */}
      <Card shadow="sm" radius="md" withBorder className="mb-8 overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div className="from-primary-color/10 to-primary-color/5 dark:from-primary-color/20 dark:to-primary-color/10 absolute inset-0 bg-gradient-to-r rounded-lg"></div>

          <div className="p-6 md:p-8">
            <div className="flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
              <div className="flex items-center gap-4">
                <Avatar
                  size={80}
                  radius="md"
                  src={null}
                  color="blue"
                  className="border border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-6"
                  styles={{
                    root: {
                      backgroundColor: "var(--mantine-color-dark-6) !important",
                      borderColor: "var(--mantine-color-dark-4) !important",
                    },
                  }}
                >
                  {typeof jobDetails.company === "string"
                    ? jobDetails.company.substring(0, 2).toUpperCase()
                    : jobDetails.company.name.substring(0, 2).toUpperCase()}
                </Avatar>

                <div className="space-y-2">
                  <Title
                    order={1}
                    className="text-primary-color text-2xl font-bold md:text-3xl"
                  >
                    {jobDetails.title}
                  </Title>
                  <Group gap="lg">
                    <Group gap="xs">
                      <BiBuildings className="text-primary-color h-5 w-5" />
                      <Text className="font-medium text-gray-800 dark:text-gray-200">
                        {typeof jobDetails.company === "string"
                          ? jobDetails.company
                          : jobDetails.company.name}
                      </Text>
                    </Group>
                    <Group gap="xs">
                      <BiMapPin className="text-primary-color h-5 w-5" />
                      <Text className="text-gray-800 dark:text-gray-200">
                        {jobDetails.location}
                      </Text>
                    </Group>
                  </Group>
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <Tooltip label="Save Job">
                  <Button
                    variant="outline"
                    color="gray"
                    radius="xl"
                    size="md"
                    onClick={handleSaveJob}
                    loading={isButtonLoading.save}
                  >
                    {!isButtonLoading.save && (
                      <FaRegBookmark className="h-5 w-5" />
                    )}
                  </Button>
                </Tooltip>
                <Tooltip label="Share Job">
                  <Button
                    variant="outline"
                    color="gray"
                    radius="xl"
                    size="md"
                    onClick={() => {
                      // In a real implementation, you would open a share dialog
                      navigator.clipboard.writeText(window.location.href);
                      notifications.show({
                        title: "Link Copied",
                        message: "Job link copied to clipboard",
                        color: "blue",
                      });
                    }}
                  >
                    <FaRegShareSquare className="h-5 w-5" />
                  </Button>
                </Tooltip>
                <Button
                  onClick={async () => {
                    if (hasApplied) return; // Don't do anything if already applied

                    setIsButtonLoading((prev) => ({ ...prev, apply: true }));
                    try {
                      const success = await handleApply();
                      if (success) {
                        // Navigate to applications page on success
                        navigate("/candidate/applications");
                      }
                    } finally {
                      setIsButtonLoading((prev) => ({ ...prev, apply: false }));
                    }
                  }}
                  leftSection={
                    !isButtonLoading.apply &&
                    !isSubmitting &&
                    !hasApplied && <BiBriefcase className="h-5 w-5" />
                  }
                  radius="xl"
                  size="md"
                  className={`${hasApplied ? "bg-gray-500" : "bg-primary-color hover:bg-primary-color/90"}`}
                  loading={isButtonLoading.apply || isSubmitting}
                  disabled={hasApplied || isChecking}
                >
                  {isButtonLoading.apply || isSubmitting
                    ? ""
                    : hasApplied
                      ? "Already Applied"
                      : "Apply Now"}
                </Button>
              </div>
            </div>

            <div className="mt-6 flex flex-wrap items-center gap-4 border-t border-gray-200 dark:border-gray-700 pt-4">
              <Badge color="blue" size="lg" radius="sm" className="px-3 py-1.5">
                {jobDetails.type}
              </Badge>
              <Text
                size="sm"
                className="text-gray-700 dark:text-gray-300 flex items-center"
              >
                <BiCalendar className="mr-1.5 inline h-4 w-4 text-primary-color" />
                Posted:{" "}
                {new Date(
                  jobDetails.datePosted || jobDetails.createdAt || "",
                ).toLocaleDateString()}
              </Text>
              {jobDetails.applicationDeadline && (
                <Text
                  size="sm"
                  className="text-gray-700 dark:text-gray-300 flex items-center"
                >
                  <BiCalendar className="mr-1.5 inline h-4 w-4 text-primary-color" />
                  Deadline:{" "}
                  {new Date(
                    jobDetails.applicationDeadline,
                  ).toLocaleDateString()}
                </Text>
              )}
              <Badge
                color="indigo"
                size="lg"
                radius="sm"
                className="px-3 py-1.5"
              >
                {jobDetails.experience} Experience
              </Badge>
            </div>
          </div>
        </div>
      </Card>

      <Grid gutter="lg">
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Stack gap="lg">
            {/* Job Description */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="blue" variant="light">
                    <BiInfoCircle className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Job Description
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <Text className="leading-relaxed whitespace-pre-line text-gray-900 dark:text-gray-300">
                  {jobDetails.description}
                </Text>
              </div>
            </Card>

            {/* Requirements */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="indigo"
                    variant="light"
                  >
                    <BiSolidGraduation className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Requirements
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                {typeof jobDetails.requirements === "string" ? (
                  <Text className="leading-relaxed whitespace-pre-line text-gray-900 dark:text-gray-300">
                    {jobDetails.requirements}
                  </Text>
                ) : (
                  <List
                    spacing="sm"
                    icon={
                      <ThemeIcon color="indigo" size="sm" radius="xl">
                        <FaCheck size={10} />
                      </ThemeIcon>
                    }
                  >
                    {Array.isArray(jobDetails.requirements) &&
                      (jobDetails.requirements as string[]).map(
                        (req: string, index: number) => (
                          <List.Item
                            key={index}
                            className={getClasses(
                              "text-gray-900 font-medium",
                              "text-gray-300 font-medium",
                            )}
                          >
                            {req}
                          </List.Item>
                        ),
                      )}
                  </List>
                )}
              </div>
            </Card>

            {/* Skills */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/30 dark:to-blue-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="cyan" variant="light">
                    <BiGlobe className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Required Skills
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="flex flex-wrap gap-2">
                  {jobDetails.skills &&
                    Array.isArray(jobDetails.skills) &&
                    jobDetails.skills.map((skill: string, index: number) => (
                      <Badge
                        key={index}
                        size="lg"
                        radius="sm"
                        variant="light"
                        color="cyan"
                        className="px-3 py-1.5"
                      >
                        {skill}
                      </Badge>
                    ))}
                </div>
              </div>
            </Card>

            {/* Benefits */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="teal" variant="light">
                    <BiGroup className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Benefits
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                {jobDetails.benefits && Array.isArray(jobDetails.benefits) ? (
                  <List
                    spacing="sm"
                    icon={
                      <ThemeIcon color="teal" size="sm" radius="xl">
                        <FaCheck size={10} />
                      </ThemeIcon>
                    }
                  >
                    {(jobDetails.benefits as string[]).map(
                      (benefit: string, index: number) => (
                        <List.Item
                          key={index}
                          className={getClasses(
                            "text-gray-900 font-medium",
                            "text-gray-300 font-medium",
                          )}
                        >
                          {benefit}
                        </List.Item>
                      ),
                    )}
                  </List>
                ) : (
                  <Text className="text-gray-900 dark:text-gray-300">
                    Information about benefits not available.
                  </Text>
                )}
              </div>
            </Card>
          </Stack>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Stack gap="lg">
            {/* Match Score Card */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="green"
                    variant="light"
                  >
                    <BiTrophy className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Match Score
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="mb-2 text-center">
                  <Text size="xl" fw={700} className="text-green-600">
                    {matchScore}%
                  </Text>
                  <Text size="sm" c="dimmed">
                    Match with your profile
                  </Text>
                </div>
                <Progress
                  value={matchScore}
                  color={
                    matchScore > 80
                      ? "green"
                      : matchScore > 60
                        ? "yellow"
                        : "red"
                  }
                  size="xl"
                  radius="xl"
                  className="mb-3"
                />
                <Text
                  size="sm"
                  className="text-center text-gray-700 dark:text-gray-400"
                >
                  Your skills and experience are a great match for this
                  position!
                </Text>
              </div>
            </Card>

            {/* Job Details Card */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon size="lg" radius="md" color="blue" variant="light">
                    <BiInfoCircle className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    Job Details
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <div className="space-y-4">
                  {jobDetails.salary && (
                    <Group gap="xs">
                      <ThemeIcon
                        size="md"
                        radius="xl"
                        color="green"
                        variant="light"
                      >
                        <BiDollar className="h-4 w-4" />
                      </ThemeIcon>
                      <Text className="text-gray-900 font-medium dark:text-gray-300">
                        <span className="font-medium">Salary:</span>{" "}
                        {jobDetails.salary}
                      </Text>
                    </Group>
                  )}
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="blue"
                      variant="light"
                    >
                      <BiTime className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Job Type:</span>{" "}
                      {jobDetails.type}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="indigo"
                      variant="light"
                    >
                      <BiCheckCircle className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Status:</span>{" "}
                      {jobDetails.status}
                    </Text>
                  </Group>
                  <Group gap="xs">
                    <ThemeIcon
                      size="md"
                      radius="xl"
                      color="violet"
                      variant="light"
                    >
                      <BiUser className="h-4 w-4" />
                    </ThemeIcon>
                    <Text className="text-gray-900 font-medium dark:text-gray-300">
                      <span className="font-medium">Experience:</span>{" "}
                      {jobDetails.experience}
                    </Text>
                  </Group>
                </div>
              </div>
            </Card>

            {/* Company Information */}
            <Card
              shadow="sm"
              radius="md"
              withBorder
              className="overflow-hidden"
            >
              <div className="border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/30 dark:to-orange-900/30 p-4">
                <Group gap="xs">
                  <ThemeIcon
                    size="lg"
                    radius="md"
                    color="orange"
                    variant="light"
                  >
                    <BiBuildings className="h-5 w-5" />
                  </ThemeIcon>
                  <Title
                    order={2}
                    className="text-xl font-bold text-gray-800 dark:text-gray-100"
                  >
                    About{" "}
                    {companyInfo?.name ||
                      (typeof jobDetails.company === "string"
                        ? jobDetails.company
                        : jobDetails.company.name)}
                  </Title>
                </Group>
              </div>
              <div className="p-5">
                <Text className="!mb-4 text-gray-900 dark:text-gray-300">
                  {companyInfo?.description ||
                    "No company description available."}
                </Text>
                <div className="space-y-2">
                  {companyInfo?.location && (
                    <Group gap="xs">
                      <ThemeIcon
                        size="sm"
                        radius="xl"
                        color="orange"
                        variant="light"
                      >
                        <FaCheck size={8} />
                      </ThemeIcon>
                      <Text
                        size="sm"
                        className="text-gray-900 dark:text-gray-300"
                      >
                        <span className="font-medium">Location:</span>{" "}
                        {companyInfo.location}
                      </Text>
                    </Group>
                  )}

                  {/* Add more company details if available */}
                  <Group gap="xs">
                    <ThemeIcon
                      size="sm"
                      radius="xl"
                      color="orange"
                      variant="light"
                    >
                      <FaCheck size={8} />
                    </ThemeIcon>
                    <Text
                      size="sm"
                      className="text-gray-900 dark:text-gray-300"
                    >
                      <span className="font-medium">Company:</span>{" "}
                      {typeof jobDetails.company === "string"
                        ? jobDetails.company
                        : jobDetails.company.name}
                    </Text>
                  </Group>
                  {companyInfo?.website && (
                    <Group gap="xs">
                      <ThemeIcon
                        size="sm"
                        radius="xl"
                        color="orange"
                        variant="light"
                      >
                        <FaCheck size={8} />
                      </ThemeIcon>
                      <Text
                        size="sm"
                        className="text-gray-900 dark:text-gray-300"
                      >
                        <span className="font-medium">Website:</span>{" "}
                        <a
                          href={
                            companyInfo.website.startsWith("http")
                              ? companyInfo.website
                              : `https://${companyInfo.website}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-color hover:underline"
                        >
                          {companyInfo.website}
                        </a>
                      </Text>
                    </Group>
                  )}
                </div>
              </div>
            </Card>
          </Stack>
        </Grid.Col>
      </Grid>
    </PageContainer>
  );
}
