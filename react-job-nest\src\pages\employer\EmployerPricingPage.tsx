"use client";

import { <PERSON><PERSON><PERSON><PERSON>, PageHeading } from "@/design-system/components";
import { useThemeClasses } from "@/design-system/utils/theme-utils";
import {
  Accordion,
  Badge,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  List,
  Text,
  ThemeIcon,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { useState } from "react";
import {
  FaCheck,
  FaCrown,
  FaQuestionCircle,
  FaRocket,
  FaStar,
  FaTimes,
} from "react-icons/fa";
import { Link } from "react-router";

// Pricing plans data
const pricingPlans = [
  {
    id: "basic",
    name: "Basic",
    description: "Perfect for startups and small businesses",
    activeJobs: 2,
    price: 500,
    billingPeriod: "month",
    color: "gray",
    icon: FaCheck,
    features: [
      { text: "2 active job postings", included: true },
      { text: "30-day job visibility", included: true },
      { text: "50 CV views per month", included: true },
      { text: "Basic candidate filtering", included: true },
      { text: "Email notifications", included: true },
      { text: "Standard job listing", included: true },
      { text: "Basic analytics", included: true },
      { text: "Priority placement", included: false },
      { text: "Featured company profile", included: false },
      { text: "Candidate matching", included: false },
    ],
    cta: "Get Started",
    popular: false,
    annualDiscount: "Save 15%",
    annualPrice: 5089,
  },
  {
    id: "professional",
    name: "Professional",
    description: "Ideal for growing companies with regular hiring",
    activeJobs: 5,
    price: 1000,
    billingPeriod: "month",
    color: "primary",
    icon: FaStar,
    features: [
      { text: "5 active job postings", included: true },
      { text: "45-day job visibility", included: true },
      { text: "200 CV views per month", included: true },
      { text: "Advanced candidate filtering", included: true },
      { text: "Email and SMS notifications", included: true },
      { text: "Enhanced job listing", included: true },
      { text: "Detailed analytics dashboard", included: true },
      { text: "Priority placement", included: true },
      { text: "Featured company profile", included: true },
      { text: "Basic candidate matching", included: true },
    ],
    cta: "Get Started",
    popular: true,
    annualDiscount: "Save 20%",
    annualPrice: 9590,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "For organizations with extensive hiring needs",
    activeJobs: "Unlimited",
    price: 2500,
    billingPeriod: "month",
    color: "accent",
    icon: FaCrown,
    features: [
      { text: "Unlimited active job postings", included: true },
      { text: "60-day job visibility", included: true },
      { text: "Unlimited CV views", included: true },
      { text: "Advanced candidate filtering", included: true },
      { text: "Email, SMS, and in-app notifications", included: true },
      { text: "Premium job listing", included: true },
      { text: "Advanced analytics and reporting", included: true },
      { text: "Top priority placement", included: true },
      { text: "Featured company profile", included: true },
      { text: "AI-powered candidate matching", included: true },
    ],
    cta: "Contact Sales",
    popular: false,
    annualDiscount: "Save 25%",
    annualPrice: 22491,
  },
];

// FAQ data
const faqItems = [
  {
    question: "What are active job postings?",
    answer:
      "Active job postings are the number of job listings you can have published simultaneously. For example, with the Professional plan, you can have up to 5 jobs posted at the same time. When you close or fill a position, you can post a new one in its place.",
  },
  {
    question: "What are CV views?",
    answer:
      "CV views allow you to view detailed candidate profiles and contact information. Each time you view a new candidate's full CV, it counts toward your monthly limit. The counter resets at the beginning of each billing cycle.",
  },
  {
    question: "Can I switch between monthly and annual billing?",
    answer:
      "Yes, you can switch between monthly and annual billing at any time. If you switch to annual billing, you'll immediately benefit from the discount. If you switch from annual to monthly, the change will take effect at the end of your current annual period.",
  },
  {
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards, bank transfers, and mobile payment methods for all subscription plans.",
  },
  {
    question: "Can I upgrade or downgrade my plan?",
    answer:
      "Yes, you can upgrade your plan at any time, and the new features will be available immediately. If you downgrade, the change will take effect at the start of your next billing cycle. We'll prorate any difference in subscription costs.",
  },
  {
    question: "What kind of support is included?",
    answer:
      "All plans include email support. Professional plans include priority support, and Enterprise plans include a dedicated account manager to help optimize your recruitment process.",
  },
];

export default function EmployerPricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<"month" | "year">("month");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Format price with currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-EG", {
      style: "currency",
      currency: "EGP",
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get price based on billing period
  const getPrice = (plan: any) => {
    if (billingPeriod === "month") {
      return formatPrice(plan.price);
    } else {
      return formatPrice(plan.annualPrice);
    }
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Pricing" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Employer Subscription Plans"
          subtitle="Choose the perfect plan for your hiring needs"
          className="mb-0 text-center"
          variant="employer"
        />
      </Box>

      {/* Billing Toggle */}
      <div className="mb-8 flex flex-col items-center">
        <Text
          size="lg"
          fw={500}
          className={useThemeClasses(
            "text-gray-700 mb-4",
            "text-gray-200 mb-4",
          )}
        >
          Choose the right plan for your hiring needs
        </Text>
        <Group className="my-5">
          <Button
            variant={billingPeriod === "month" ? "filled" : "outline"}
            onClick={() => setBillingPeriod("month")}
            radius="md"
          >
            Monthly Billing
          </Button>
          <Button
            variant={billingPeriod === "year" ? "filled" : "outline"}
            onClick={() => setBillingPeriod("year")}
            radius="md"
          >
            Annual Billing
            <Badge color="green" variant="filled" size="sm" ml={5}>
              Save up to 25%
            </Badge>
          </Button>
        </Group>
        <Text
          size="sm"
          className={useThemeClasses(
            "text-gray-500 mt-4",
            "text-gray-400 mt-4",
          )}
        >
          All plans include access to our talent pool of over 1 million
          candidates
        </Text>
      </div>

      {/* Pricing Cards */}
      <Grid gutter="xl">
        {pricingPlans.map((plan) => (
          <Grid.Col key={plan.id} span={{ base: 12, md: 6, lg: 4 }}>
            <Card
              withBorder
              radius="md"
              className={`h-full transition-all duration-300 ${
                plan.popular
                  ? isDark
                    ? "shadow-dark-lg ring-2 ring-primary-500"
                    : "shadow-lg ring-2 ring-primary-500"
                  : isDark
                    ? "shadow-dark-sm hover:shadow-dark-md"
                    : "shadow-sm hover:shadow-md"
              }`}
              padding="xl"
            >
              {plan.popular && (
                <Badge
                  color="primary"
                  variant="filled"
                  className="absolute right-4 top-4"
                >
                  Most Popular
                </Badge>
              )}

              <ThemeIcon
                size="xl"
                radius="md"
                color={plan.color}
                variant="light"
                className="mb-4"
              >
                <plan.icon size={20} />
              </ThemeIcon>

              <Title order={3} className="mb-1">
                {plan.name}
              </Title>
              <Text c="dimmed" size="sm" className="mb-4">
                {plan.description}
              </Text>

              <div className="mb-6">
                <Group align="flex-end" gap={5}>
                  <Text size="xl" fw={700} className="text-3xl">
                    {getPrice(plan)}
                  </Text>
                  <Text c="dimmed" size="sm">
                    /{billingPeriod}
                  </Text>
                </Group>
                <Group gap={5} className="mt-1">
                  <Text c="dimmed" size="sm">
                    {typeof plan.activeJobs === "number"
                      ? plan.activeJobs
                      : plan.activeJobs}{" "}
                    active job postings
                  </Text>
                </Group>
                {billingPeriod === "year" && (
                  <Badge color="green" variant="light" className="mt-2">
                    {plan.annualDiscount}
                  </Badge>
                )}
              </div>

              <Divider className="mb-4" />

              <List
                spacing="sm"
                size="sm"
                center
                className="mb-6"
                styles={{ itemWrapper: { display: "flex" } }}
              >
                {plan.features.map((feature, index) => (
                  <List.Item
                    key={index}
                    icon={
                      feature.included ? (
                        <ThemeIcon
                          color="green"
                          size={20}
                          radius="xl"
                          variant="light"
                        >
                          <FaCheck size={10} />
                        </ThemeIcon>
                      ) : (
                        <ThemeIcon
                          color="gray"
                          size={20}
                          radius="xl"
                          variant="light"
                        >
                          <FaTimes size={10} />
                        </ThemeIcon>
                      )
                    }
                    className={
                      !feature.included
                        ? useThemeClasses("text-gray-500", "text-gray-600")
                        : ""
                    }
                  >
                    {feature.text}
                  </List.Item>
                ))}
              </List>

              <Button
                fullWidth
                radius="md"
                size="md"
                color={plan.color}
                component={Link}
                to={
                  plan.id === "enterprise"
                    ? "/contact-us"
                    : `/employer/checkout?plan=${plan.id}&billing=${billingPeriod}`
                }
                className="mt-5"
              >
                {plan.cta}
              </Button>
            </Card>
          </Grid.Col>
        ))}
      </Grid>

      {/* FAQ Section */}
      <div className="mt-16">
        <Title order={2} className="!mb-6 text-center">
          Frequently Asked Questions
        </Title>
        <Accordion variant="separated" radius="md">
          {faqItems.map((item, index) => (
            <Accordion.Item key={index} value={`item-${index}`}>
              <Accordion.Control>
                <Group gap="xs">
                  <FaQuestionCircle size={16} className="text-primary-color" />
                  <span>{item.question}</span>
                </Group>
              </Accordion.Control>
              <Accordion.Panel>{item.answer}</Accordion.Panel>
            </Accordion.Item>
          ))}
        </Accordion>
      </div>

      {/* CTA Section */}
      <Card
        withBorder
        radius="md"
        className={useThemeClasses(
          "mt-16 bg-gradient-to-r from-primary-50 to-accent-50 p-8 text-center",
          "mt-16 bg-gradient-to-r from-dark-6 to-dark-5 p-8 text-center",
        )}
      >
        <Title order={3} className="mb-2">
          Need a Custom Solution?
        </Title>
        <Text className="!my-6 max-w-2xl !mx-auto">
          Looking for a tailored solution? Our Enterprise Plus plan offers
          custom features for high-volume recruiters and organizations with
          specific hiring needs.
        </Text>
        <Group justify="center" gap="md">
          <Button
            size="lg"
            radius="md"
            leftSection={<FaRocket size={16} />}
            component={Link}
            to="/contact-us"
          >
            Contact Sales
          </Button>
          <Button
            size="lg"
            radius="md"
            variant="outline"
            component={Link}
            to="/employer/checkout?plan=basic&billing=month&trial=true"
          >
            Start Free Trial
          </Button>
        </Group>
        <Text
          size="sm"
          className={useThemeClasses(
            "!mt-4 text-gray-600",
            "!mt-4 text-gray-400",
          )}
        >
          Try our platform with 1 active job posting and 10 CV views free for 14
          days
        </Text>
      </Card>
    </PageContainer>
  );
}
